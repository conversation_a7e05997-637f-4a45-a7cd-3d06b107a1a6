/**
 * 视觉脚本系统模块
 * 导出所有视觉脚本系统相关的类和接口
 */

// 核心系统
export * from './VisualScriptSystem';
export * from './VisualScriptEngine';
export * from './VisualScriptComponent';

// 节点系统
export * from './nodes/Node';
export * from './nodes/FlowNode';
export * from './nodes/EventNode';
export * from './nodes/FunctionNode';
export * from './nodes/AsyncNode';
export * from './nodes/NodeRegistry';

// 图形系统
export * from './graph/Graph';
export * from './graph/GraphJSON';

// 执行系统
export * from './execution/Fiber';
export * from './execution/ExecutionContext';

// 事件系统
export * from './events/CustomEvent';

// 值类型系统
export * from './values/ValueTypeRegistry';
export * from './values/Variable';

// 工具类
// 注意：Logger 和 Assert 工具类文件不存在，已移除导出

// 编辑器集成
// 注意：NodeEditor 和 GraphEditor 文件不存在，已移除导出

// 预设节点 - 使用具名导出避免冲突
export * from './presets/MathNodes';
export * from './presets/EntityNodes';
export * from './presets/PhysicsNodes';
export * from './presets/AudioNodes';
export * from './presets/NetworkNodes';
export * from './presets/HTTPNodes';
export * from './presets/JSONNodes';
export * from './presets/DateTimeNodes';
export * from './presets/UINodes';
export * from './presets/AdvancedUINodes';
export * from './presets/FileSystemNodes';
export * from './presets/AdvancedFileSystemNodes';
export * from './presets/ImageProcessingNodes';
export * from './presets/AdvancedImageNodes';
export * from './presets/DatabaseNodes';
export * from './presets/CryptographyNodes';

// 核心节点 - 显式导出以避免与其他模块冲突
export {
  SequenceNode as CoreSequenceNode,
  BranchNode as CoreBranchNode,
  DelayNode as CoreDelayNode,
  ForLoopNode as CoreForLoopNode,
  WhileLoopNode as CoreWhileLoopNode
} from './presets/CoreNodes';

// 动画节点 - 显式导出以避免冲突
export {
  registerAnimationNodes
} from './presets/AnimationNodes';

// 输入节点 - 显式导出以避免冲突
export {
  registerInputNodes
} from './presets/InputNodes';

// 逻辑节点 - 显式导出以避免与CoreNodes冲突
export {
  ComparisonNode,
  LogicalOperationNode,
  ToggleNode,
  BranchNode as LogicBranchNode  // 重命名以避免冲突
} from './presets/LogicNodes';

// 时间节点 - 显式导出以避免与CoreNodes冲突
export {
  GetTimeNode,
  TimerNode,
  DelayNode as TimeDelayNode,  // 重命名以避免冲突
  IntervalNode,
  TimeCompareNode,
  TimeFormatNode,
  TimeScaleNode,
  TimeInterpolateNode,
  TimeSchedulerNode,
  registerTimeNodes
} from './presets/TimeNodes';
